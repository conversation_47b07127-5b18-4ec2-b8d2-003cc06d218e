import { Global, Module } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { createClient } from 'redis';

@Global()
@Module({
  providers: [
    {
      provide: 'REDIS_CLIENT',
      useFactory: async (configService: ConfigService) => {
        const redisUrl = configService.get<string>('REDIS_URI');
        const client = createClient({
          url: redisUrl,
        });

        try {
          await client.connect();
          console.log('Redis connected successfully');
        } catch (error) {
          console.error('Redis connection failed:', error.message);
          throw new Error(`Failed to connect to Redis: ${error.message}`);
        }

        return client;
      },
      inject: [ConfigService],
    },
  ],
  exports: ['REDIS_CLIENT'],
})
export class RedisModule {}
