import {
  Injectable,
  NestMiddleware,
  UnauthorizedException,
} from '@nestjs/common';
import { NextFunction, Request, Response } from 'express';

@Injectable()
export class BlockRequestMiddleware implements NestMiddleware {
  use(req: Request, res: Response, next: NextFunction): void {
    const { method, originalUrl, ip, headers } = req;
    const userAgent = headers['user-agent'] || '';
    const startTime = Date.now();

    // Kiểm tra xem header có chứa 'app-code' không
    const appCode = headers['app-code'];
    if (process.env.HEADER_APP_ACCESS && process.env.HEADER_APP_ACCESS.trim() !== '') {
      if (appCode !== process.env.HEADER_APP_ACCESS) {
        // Nếu 'app-code' không hợp lệ, trả về lỗi 403 Forbidden
        throw new UnauthorizedException('Invalid app-code header');
      }
    }

    res.on('finish', () => {
      const { statusCode } = res;
      const elapsedTime = Date.now() - startTime;
      console.log(
        `${method} ${originalUrl} - ${statusCode} - ${elapsedTime}ms - IP: ${ip} - User-Agent: ${userAgent}`,
      );
    });

    next();
  }
}
