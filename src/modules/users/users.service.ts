import {
  Injectable,
  UnauthorizedException,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Request } from 'express';
import { UpdateUserDto } from 'src/dtos/user/update-user.dto';
import { DeleteUserResponseDto } from 'src/dtos/user/delete-user.dto';
import { StoryEntity } from 'src/entities/story.entity';
import { TemplateType } from 'src/shared/enums/template-type.enum';
import { HelperUtils } from 'src/shared/helpers/utils.helper';
import { CheckPassword, HashPassword } from 'wordpress-hash-node';
import { StoryService } from '../stories/stories.service';
import { PrismaService } from './../../prisma/prisma.service';

@Injectable()
export class UserService {
  constructor(
    private prisma: PrismaService,
    private readonly storyService: StoryService,
    private readonly configService: ConfigService,
  ) {}

  async detailUserByUserLogin(user_login) {
    const user = await this.prisma.wpva_users.findFirst({
      where: {
        user_login: user_login,
      },
      select: {
        user_login: true,
      },
    });

    return user;
  }

  async updateUser(userId: number, body: UpdateUserDto) {
    const {
      email,
      first_name,
      last_name,
      new_password,
      old_password,
      banner_url,
      avatar_url,
    } = body;

    const user = await this.detailUserById(userId, true);
    const user_pass = user.user_pass;
    const dataUpdateUser: any = {};

    if (old_password && new_password) {
      const isPasswordValid = CheckPassword(old_password, user_pass);
      if (!isPasswordValid) {
        throw new UnauthorizedException('Mật khẩu cũ không chính xác!!!');
      }
      const passwordHash = await HashPassword(new_password);
      dataUpdateUser['user_pass'] = passwordHash;
    }

    if (email && email !== user.user_email) {
      dataUpdateUser['user_email'] = email;
    }

    // Update wpva_users table if necessary
    if (Object.keys(dataUpdateUser).length > 0) {
      await this.prisma.wpva_users.update({
        where: { ID: BigInt(userId) },
        data: dataUpdateUser,
      });
    }

    // Update wpva_usermeta table for first_name and last_name
    const userMetaUpdates = [];

    if (last_name && last_name !== user['last_name']) {
      userMetaUpdates.push(
        this.prisma.wpva_usermeta.updateMany({
          where: {
            user_id: BigInt(userId),
            meta_key: 'last_name',
          },
          data: {
            meta_value: last_name,
          },
        }),
      );
    }

    if (first_name && first_name !== user['first_name']) {
      userMetaUpdates.push(
        this.prisma.wpva_usermeta.updateMany({
          where: {
            user_id: BigInt(userId),
            meta_key: 'first_name',
          },
          data: {
            meta_value: first_name,
          },
        }),
      );
    }

    // Execute all user meta updates
    if (userMetaUpdates.length > 0) {
      await Promise.all(userMetaUpdates);
    }

    return { message: 'User updated successfully' };
  }

  async detailUserById(user_id, is_select_password: boolean) {
    // Fetch user data
    const user = await this.prisma.wpva_users.findFirst({
      where: {
        ID: BigInt(user_id),
      },
      select: {
        ID: true,
        user_login: true,
        user_nicename: true,
        user_email: true,
        user_url: true,
        user_registered: true,
        user_activation_key: true,
        user_status: true,
        display_name: true,
        user_pass: is_select_password,
      },
    });

    // Fetch user metadata
    const user_meta = await this.prisma.wpva_usermeta.findMany({
      where: {
        user_id: BigInt(user_id),
        meta_key: {
          in: [
            'ihc_user_custom_banner_src',
            'ihc_avatar',
            'last_name',
            'first_name',
            'wpva_capabilities',
          ],
        },
      },
      select: {
        meta_key: true,
        meta_value: true,
      },
    });

    // Initialize user_meta_value object
    const user_meta_value = {};

    let role = '';

    // Process user metadata
    for (const item of user_meta) {
      const key = item.meta_key;
      const value = item.meta_value;

      if (key === 'ihc_user_custom_banner_src') {
        user_meta_value['banner'] = value;
      } else if (key === 'ihc_avatar') {
        const post_avatar = await this.prisma.wpva_posts.findFirst({
          where: {
            ID: BigInt(value),
          },
          select: {
            guid: true,
          },
        });
        if (post_avatar) {
          user_meta_value['avatar'] = post_avatar.guid;
        }
      } else if (key === 'wpva_capabilities') {
        role = HelperUtils.getRoleByWpCapabilities(value)?.[0] ?? '';
        user_meta_value['role'] = role;
      } else {
        user_meta_value[key] = value;
      }
    }

    // Return combined user data and metadata
    return { ...user, ...user_meta_value };
  }

  async getPostIdsFavoritesById(user_id: number) {
    const simpleFavorites = await this.prisma.wpva_usermeta.findFirst({
      where: {
        user_id: user_id,
        meta_key: 'simplefavorites',
      },
      select: {
        meta_value: true,
      },
    });
    if (!simpleFavorites) {
      return [];
    }

    const unserializeSimpleFavoritesValue = HelperUtils.unserializeString(
      simpleFavorites.meta_value,
    );
    console.log(unserializeSimpleFavoritesValue);

    if (!unserializeSimpleFavoritesValue) {
      return [];
    }
    const postIds = unserializeSimpleFavoritesValue[0]?.posts;
    console.log(typeof postIds);
    return Array.isArray(postIds) ? postIds : [];
  }

  async getSimpleFavorites(req: Request, user_id: number) {
    const postIds = await this.getPostIdsFavoritesById(user_id);
    if (postIds.length > 0) {
      const posts = await this.storyService.detailPostByIDS(
        req,
        postIds,
        user_id,
      );
      return posts.map((story) => new StoryEntity(story));
    }
    return [];
  }

  async deleteSimpleFavorites(user_id: number, req: Request, post_id?: number) {
    // Điều kiện cơ bản bắt buộc
    const whereCondition: any = {
      user_id: user_id,
      meta_key: 'simplefavorites',
    };
    const simpleFavorites = await this.prisma.wpva_usermeta.findFirst({
      where: whereCondition,
      select: {
        meta_value: true,
        umeta_id: true,
      },
    });

    if (!simpleFavorites) {
      console.log('No simple favorites found for user:', user_id);
      return [];
    }

    const unserializeSimpleFavoritesValue = HelperUtils.unserializeString(
      simpleFavorites.meta_value,
    );

    console.log('Unserialized value:', unserializeSimpleFavoritesValue);

    const umeta_id = simpleFavorites.umeta_id;
    // Lấy danh sách post_ids
    let postIds = unserializeSimpleFavoritesValue[0]?.posts;
    let groupPosts = unserializeSimpleFavoritesValue[0]?.groups[0]?.posts;

    // In ra giá trị của postIds và groupPosts sau khi unserialize
    console.log('postIds:', postIds);
    console.log('groupPosts:', groupPosts);

    // Kiểm tra nếu không phải là mảng thì khởi tạo mảng rỗng
    if (!Array.isArray(postIds)) {
      postIds = [];
    }

    if (!Array.isArray(groupPosts)) {
      groupPosts = [];
    }

    // In ra giá trị của post_id trước khi xóa

    let updatedMetaValue = 'a:0:{}';
    let postQueryIds = postIds;
    if (post_id !== undefined) {
      postQueryIds = [post_id];

      const postIndex = postIds.indexOf(Number(post_id));
      const groupPostIndex = groupPosts.indexOf(Number(post_id));

      if (postIndex > -1) {
        postIds.splice(postIndex, 1);
      }

      if (groupPostIndex > -1) {
        groupPosts.splice(groupPostIndex, 1);
      }

      unserializeSimpleFavoritesValue[0].posts = postIds;
      unserializeSimpleFavoritesValue[0].groups[0].posts = groupPosts;
      updatedMetaValue = HelperUtils.serializeString(
        unserializeSimpleFavoritesValue,
      );
    }

    await this.prisma.wpva_usermeta.update({
      where: {
        umeta_id: umeta_id,
      },
      data: {
        meta_value: updatedMetaValue,
      },
    });

    console.log('Updated meta value:', updatedMetaValue);
    console.log('postQueryIds:', postQueryIds);
    if (postQueryIds.length > 0) {
      const metas = await this.prisma.wpva_postmeta.findMany({
        where: {
          post_id: { in: postQueryIds },
          meta_key: 'simplefavorites_count',
        },
      });

      for (const id of postQueryIds) {
        console.log(id);
        const meta = metas.find((m) => m.post_id == id);
        console.log(meta);
        if (meta) {
          await this.prisma.wpva_postmeta.update({
            where: { meta_id: meta.meta_id },
            data: {
              meta_value: String(Math.max(Number(meta.meta_value) - 1, 0)),
            },
          });
        }
      }
    }

    return postIds;
  }
  async addSimpleFavorites(
    user_id: number,
    req: Request,
    post_id?: number,
    typeUpdate?: string,
  ) {
    // Điều kiện cơ bản bắt buộc
    if (typeUpdate === 'remove') {
      return await this.deleteSimpleFavorites(user_id, req, post_id);
    }

    const whereCondition: any = {
      user_id: user_id,
      meta_key: 'simplefavorites',
    };

    const simpleFavorites = await this.prisma.wpva_usermeta.findFirst({
      where: whereCondition,
      select: {
        meta_value: true,
        umeta_id: true,
      },
    });
    if (!simpleFavorites || simpleFavorites.meta_value == 'a:0:{}') {
      // Nếu không có giá trị, thực hiện thêm mới với post_id
      const newFavorites = [
        {
          site_id: 1,
          posts: [post_id],
          groups: [
            {
              group_id: 1,
              site_id: 1,
              group_name: 'Default List',
              posts: [post_id],
            },
          ],
        },
      ];

      const serializedNewFavorites = HelperUtils.serializeString(newFavorites);
      if (!simpleFavorites) {
        await this.prisma.wpva_usermeta.create({
          data: {
            user_id: user_id,
            meta_key: 'simplefavorites',
            meta_value: serializedNewFavorites,
          },
        });
      } else {
        await this.prisma.wpva_usermeta.update({
          where: {
            umeta_id: simpleFavorites.umeta_id,
          },
          data: {
            meta_value: serializedNewFavorites,
          },
        });
      }
      const meta = await this.prisma.wpva_postmeta.findFirst({
        where: { post_id: post_id, meta_key: 'simplefavorites_count' },
      });
      console.log('11233', meta);

      if (meta) {
        await this.prisma.wpva_postmeta.update({
          where: { meta_id: meta.meta_id },
          data: {
            meta_value: String(Number(meta.meta_value) + 1),
          },
        });
      } else {
        // Nếu không tồn tại, tạo mới với giá trị meta_value là 1
        await this.prisma.wpva_postmeta.create({
          data: {
            post_id: post_id,
            meta_key: 'simplefavorites_count',
            meta_value: '1',
          },
        });
      }

      return [post_id];
    }

    const umeta_id = simpleFavorites.umeta_id;
    const unserializeSimpleFavoritesValue = HelperUtils.unserializeString(
      simpleFavorites.meta_value,
    );

    let postIds = unserializeSimpleFavoritesValue[0]?.posts;
    let groupPosts = unserializeSimpleFavoritesValue[0]?.groups[0]?.posts;

    if (!Array.isArray(postIds)) {
      postIds = [];
    }

    if (!Array.isArray(groupPosts)) {
      groupPosts = [];
    }

    if (!postIds.includes(post_id)) {
      postIds.push(post_id);
    }

    if (!groupPosts.includes(post_id)) {
      groupPosts.push(post_id);
    }

    unserializeSimpleFavoritesValue[0].posts = postIds;
    unserializeSimpleFavoritesValue[0].groups[0].posts = groupPosts;
    const updatedMetaValue = HelperUtils.serializeString(
      unserializeSimpleFavoritesValue,
    );

    // Lưu lại giá trị đã cập nhật vào cơ sở dữ liệu
    await this.prisma.wpva_usermeta.update({
      where: {
        umeta_id: umeta_id,
      },
      data: {
        meta_value: updatedMetaValue,
      },
    });

    const meta = await this.prisma.wpva_postmeta.findFirst({
      where: { post_id: post_id, meta_key: 'simplefavorites_count' },
    });

    if (meta) {
      await this.prisma.wpva_postmeta.update({
        where: { meta_id: meta.meta_id },
        data: {
          meta_value: String(Number(meta.meta_value) + 1),
        },
      });
    } else {
      // Nếu không tồn tại, tạo mới với giá trị meta_value là 1
      await this.prisma.wpva_postmeta.create({
        data: {
          post_id: post_id,
          meta_key: 'simplefavorites_count',
          meta_value: '1',
        },
      });
    }

    return postIds;
  }

  async getPurchasedPackages(user_id: number) {
    const orders = await this.prisma.wpva_ihc_orders.findMany({
      where: {
        uid: user_id,
      },
      include: {
        wpva_ihc_orders_meta: {
          where: {
            meta_key: {
              in: [
                'ihc_payment_type',
                'code',
                'level_label',
                'amount',
                'currency',
              ],
            },
          },
        },
      },
    });
    const results = [];
    orders.forEach((order) => {
      results.push({
        order_id: order.id,
        order_status: order.status,
        order_code: this.findMetaValue(order, 'code'),
        memberships: this.findMetaValue(order, 'level_label'),
        total_amount: this.findMetaValue(order, 'amount'),
        currency: this.findMetaValue(order, 'currency'),
        payment_method: this.findMetaValue(order, 'ihc_payment_type'),
        created_time: order.create_date,
      });
    });

    return results;
  }

  private findMetaValue(order: any, key: string): string | undefined {
    return order.wpva_ihc_orders_meta.find((meta) => meta.meta_key === key)
      ?.meta_value;
  }

  async getPageTemplates(templateType: string) {
    let field_query = 'ihc_thank_you_message';
    if (templateType === TemplateType.overview) {
      field_query = 'ihc_ap_overview_msg';
    } else if (templateType === TemplateType.help) {
      field_query = 'ihc_ap_help_msg';
    }

    const template = await this.prisma.wpva_options.findFirst({
      where: { option_name: field_query },
      select: {
        option_value: true,
      },
    });
    return template.option_value;
  }

  async getSetting(req: Request) {
    const appVersionRelease = this.configService.get<string>(
      'APP_VERSION_RELEASE',
    );

    const headerVersion = req.headers['app-version'] as string;
    const enableVipServerForAll = parseInt(
      this.configService.get<string>('ENABLE_VIP_SERVER_FOR_ALL'),
    );
    const disableAdForAllUsers = parseInt(
      this.configService.get<string>('DISABLE_AD_FOR_ALL_USERS'),
    );
    let disableFunctionPublishApp = parseInt(
      this.configService.get<string>('DISABLE_FUNCTION_PUBLISH_APP'),
    );
    if (headerVersion) {
      const v1 = headerVersion.split('.').map(Number);
      const v2 = appVersionRelease.split('.').map(Number);

      for (let i = 0; i < Math.max(v1.length, v2.length); i++) {
        const num1 = v1[i] || 0; // Nếu không có thì mặc định là 0
        const num2 = v2[i] || 0;
        if (num1 > num2) {
          disableFunctionPublishApp = 1;
          break;
        }
      }
    }

    return {
      TIME_AD_INTERVAL: parseInt(
        this.configService.get<string>('TIME_AD_INTERVAL'),
      ),
      TIME_AD_PLAY_STORIES_INTERVAL: parseInt(
        this.configService.get<string>('TIME_AD_PLAY_STORIES_INTERVAL'),
      ),
      APP_TITLE: this.configService.get<string>('APP_TITLE'),
      APP_DESCRIPTION: this.configService.get<string>('APP_DESCRIPTION'),
      MIN_AD_SPACING: parseInt(
        this.configService.get<string>('MIN_AD_SPACING'),
      ),
      ENABLE_VIP_SERVER_FOR_ALL: enableVipServerForAll,
      DISABLE_AD_FOR_ALL_USERS: disableAdForAllUsers,
      DISABLE_FUNCTION_PUBLISH_APP: disableFunctionPublishApp,
      ID_ADS_BANNER: this.configService.get<string>('ID_ADS_BANNER'),
      ID_ADS_REWARD: this.configService.get<string>('ID_ADS_REWARD'),
      IOS_REWARD_AD_ID: this.configService.get<string>('IOS_REWARD_AD_ID'),
      IOS_BANNER_AD_ID: this.configService.get<string>('IOS_BANNER_AD_ID'),
      ANDROID_AD_ID: this.configService.get<string>('ANDROID_AD_ID'),
      ANDROID_ID_ADS_BANNER: this.configService.get<string>(
        'ANDROID_ID_ADS_BANNER',
      ),
      ANDROID_ADS_REWARD: this.configService.get<string>('ANDROID_ADS_REWARD'),
      IOS_AD_ID: this.configService.get<string>('IOS_AD_ID'),
      IOS_ID_ADS_BANNER: this.configService.get<string>('IOS_ID_ADS_BANNER'),
      IOS_ADS_REWARD: this.configService.get<string>('IOS_ADS_REWARD'),
      DOMAIN_WEB: this.configService.get<string>('DOMAIN_WEB'),
      APP_VERSION_RELEASE: this.configService.get<string>(
        'APP_VERSION_RELEASE',
      ),
      FACEBOOK_URL: this.configService.get<string>('FACEBOOK_URL'),
      GROUP_CHAT_URL: this.configService.get<string>('GROUP_CHAT_URL'),
      GROUP_VIP_URL: this.configService.get<string>('GROUP_VIP_URL'),
    };
  }

  async deleteUser(userId: number): Promise<DeleteUserResponseDto> {
    // Kiểm tra user có tồn tại không
    const user = await this.prisma.wpva_users.findUnique({
      where: { ID: BigInt(userId) },
      select: {
        ID: true,
        user_login: true,
        user_nicename: true,
        user_email: true,
        display_name: true,
      },
    });

    if (!user) {
      throw new NotFoundException(`User with ID ${userId} not found`);
    }

    // Kiểm tra xem user đã được "delete" chưa (có suffix _delete)
    if (user.user_login.includes('_delete_')) {
      throw new BadRequestException(
        `User with ID ${userId} has already been deleted`,
      );
    }

    // Tạo timestamp để đảm bảo tính duy nhất
    const timestamp = Date.now();
    const deletePrefix = `_delete_${timestamp}`;

    // Cập nhật thông tin user với suffix _delete
    const updatedUser = await this.prisma.wpva_users.update({
      where: { ID: BigInt(userId) },
      data: {
        user_login: `${user.user_login}${deletePrefix}`,
        user_nicename: `${user.user_nicename}${deletePrefix}`,
        user_email: `${user.user_email}${deletePrefix}`,
        display_name: `${user.display_name}${deletePrefix}`,
        user_status: 2, // Đặt status = 2 để đánh dấu là đã deleted
      },
      select: {
        ID: true,
        user_login: true,
        user_nicename: true,
        user_email: true,
        display_name: true,
        user_status: true,
      },
    });

    return {
      message: `User with ID ${userId} has been successfully marked as deleted`,
      // updatedUser: updatedUser,
    };
  }

  async restoreUser(
    userId: number,
  ): Promise<{ message: string; restoredUser: any }> {
    // Kiểm tra user có tồn tại không
    const user = await this.prisma.wpva_users.findUnique({
      where: { ID: BigInt(userId) },
      select: {
        ID: true,
        user_login: true,
        user_nicename: true,
        user_email: true,
        display_name: true,
        user_status: true,
      },
    });

    if (!user) {
      throw new NotFoundException(`User with ID ${userId} not found`);
    }

    // Kiểm tra xem user có bị soft delete không
    if (!user.user_login.includes('_delete_')) {
      throw new BadRequestException(
        `User with ID ${userId} has not been deleted`,
      );
    }

    // Remove suffix _delete_timestamp từ các fields
    const removeDeleteSuffix = (text: string) => {
      return text.replace(/_delete_\d+$/, '');
    };

    // Restore thông tin user
    const restoredUser = await this.prisma.wpva_users.update({
      where: { ID: BigInt(userId) },
      data: {
        user_login: removeDeleteSuffix(user.user_login),
        user_nicename: removeDeleteSuffix(user.user_nicename),
        user_email: removeDeleteSuffix(user.user_email),
        display_name: removeDeleteSuffix(user.display_name),
        user_status: 0, // Đặt lại status = 0 (active)
      },
      select: {
        ID: true,
        user_login: true,
        user_nicename: true,
        user_email: true,
        display_name: true,
        user_status: true,
      },
    });

    return {
      message: `User with ID ${userId} has been successfully restored`,
      restoredUser: restoredUser,
    };
  }
}
