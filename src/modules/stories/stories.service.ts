import { Inject, Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Prisma } from '@prisma/client';
import * as crypto from 'crypto';
import { Request } from 'express';
import { RedisClientType } from 'redis';
import { FindStoryConditionQueryDto } from 'src/dtos/story/find-story-condition.dto';
import {
  BackgroundMusicEntity,
  LinksStoryEntity,
  StoryEntity,
} from 'src/entities/story.entity';
import { LinkStoryInterface } from 'src/interfaces/link-story.interface';
import { Paging } from 'src/interfaces/response-format.interface';
import { HelperUtils } from 'src/shared/helpers/utils.helper';
import { PrismaService } from '../../prisma/prisma.service';

@Injectable()
export class StoryService {
  private readonly arrFieldGetData = [
    'views_daily',
    'views_monthly',
    'views_total',
    'views_weekly',
    'so_tap',
    '_kksr_casts',
    '_kksr_avg',
    'simplefavorites_count',
    '_kksr_ratings',
  ];
  private logger = new Logger('StoryService');

  constructor(
    private prisma: PrismaService,
    private readonly configService: ConfigService,
    @Inject('REDIS_CLIENT') private readonly redisClient: RedisClientType,
  ) {}

  private genQuerySumCasePostMeta(arrMetaKey: string[]): Prisma.Sql {
    const queries = arrMetaKey.map(
      (key) =>
        Prisma.sql`SUM(CASE WHEN meta_key = ${key} THEN meta_value ELSE 0 END) AS ${Prisma.raw(key)}`,
    );

    // Join the SQL fragments with commas
    return Prisma.sql`${Prisma.join(queries, ', ')}`;
  }

  private generateCacheKeyGetTotalItems(
    searchCondition?: Prisma.Sql,
    combinedCondition?: Prisma.Sql,
    ratingCondition?: Prisma.Sql,
    episodeCondition?: Prisma.Sql,
    viewCondition?: Prisma.Sql,
    searchTerm?: string, // Add direct search term parameter
  ): string {
    if (
      !searchCondition &&
      !combinedCondition &&
      !ratingCondition &&
      !episodeCondition &&
      !viewCondition
    ) {
      return '';
    }

    const combinedStr = combinedCondition ? 'with_taxonomy' : 'no_taxonomy';
    const ratingStr = ratingCondition ? 'with_rating' : 'no_rating';
    const episodeStr = episodeCondition ? 'with_episode' : 'no_episode';
    const viewStr = viewCondition ? 'with_view' : 'no_view';

    // Use direct search term instead of parsing SQL
    const searchStr = searchTerm
      ? encodeURIComponent(searchTerm.toLowerCase().trim())
      : 'no_search';

    // Tạo khóa cache bằng cách kết hợp các điều kiện trực tiếp mà không sử dụng encodeURIComponent
    return `getTotalItems:taxonomy=${combinedStr}:rating=${ratingStr}:episode=${episodeStr}:view=${viewStr}:search=${searchStr}`;
  }

  private logCacheKeyInfo(
    functionName: string,
    search: string,
    cacheKey: string,
  ): void {
    this.logger.log(
      `${functionName} Cache Key Generated: ${cacheKey.substring(0, 100)}${cacheKey.length > 100 ? '...' : ''}`,
    );
    if (search) {
      this.logger.log(
        `Search term normalized: "${search}" -> "${encodeURIComponent(search.toLowerCase().trim())}"`,
      );
    }
  }

  private async getTotalItems(
    searchCondition?: Prisma.Sql,
    combinedCondition?: Prisma.Sql,
    ratingCondition?: Prisma.Sql,
    episodeCondition?: Prisma.Sql,
    viewCondition?: Prisma.Sql,
    cacheTotalItemKey?: string,
    searchTerm?: string, // Add searchTerm parameter
  ): Promise<number> {
    let cacheKey = cacheTotalItemKey;
    if (cacheTotalItemKey == '') {
      cacheKey = this.generateCacheKeyGetTotalItems(
        searchCondition,
        combinedCondition,
        ratingCondition,
        episodeCondition,
        viewCondition,
        searchTerm, // Pass searchTerm to generateCacheKeyGetTotalItems
      );
    }

    const cacheTTL = 3600; // Thời gian hết hạn cache tính bằng giây (ví dụ: 60 giây)
    this.logger.log(`cacheKey :: getTotalItems :: ${cacheKey}`);
    try {
      // Kiểm tra xem kết quả đã có trong cache chưa
      if (cacheKey !== '') {
        const cachedResult = await this.redisClient.get(cacheKey);
        if (cachedResult !== null) {
          this.logger.log(`Cache hit for key: ${cacheKey}`);
          return Number(cachedResult);
        }
      }

      let totalItemsResult: { count: number }[];

      if (combinedCondition) {
        // When we have taxonomy filters, use the filtered_terms CTE
        totalItemsResult = await this.prisma.$queryRaw<{ count: number }[]>`
          WITH aggregated_meta AS (
            SELECT
              post_id,
              SUM(CAST(CASE WHEN meta_key = 'cap_nhat' THEN meta_value ELSE '0' END AS UNSIGNED)) AS cap_nhat,
              SUM(CAST(CASE WHEN meta_key = '_kksr_casts' THEN meta_value ELSE '0' END AS UNSIGNED)) AS _kksr_casts,
              SUM(CAST(CASE WHEN meta_key = '_kksr_ratings' THEN meta_value ELSE '0' END AS DECIMAL(3,2))) AS _kksr_ratings,
              SUM(CAST(CASE WHEN meta_key = '_kksr_avg' THEN meta_value ELSE '0' END AS DECIMAL(10,2))) AS _kksr_avg,
              SUM(CAST(CASE WHEN meta_key = 'so_tap' THEN meta_value ELSE '0' END AS UNSIGNED)) AS so_tap,
              SUM(CAST(CASE WHEN meta_key = 'views_total' THEN meta_value ELSE '0' END AS UNSIGNED)) AS views_total
            FROM
              wpva_postmeta
            WHERE
              meta_key IN (${Prisma.join(this.arrFieldGetData)})
            GROUP BY
              post_id
          ),
          filtered_terms AS (
            SELECT
              tr.object_id,
              tt.term_taxonomy_id 
            FROM
              wpva_term_relationships AS tr
            LEFT JOIN wpva_term_taxonomy AS tt ON
              tr.term_taxonomy_id = tt.term_taxonomy_id
            ${combinedCondition}
          )
          SELECT COUNT(*) AS count
          FROM wpva_posts AS a
          INNER JOIN aggregated_meta AS b ON a.ID = b.post_id
          INNER JOIN filtered_terms AS terms ON a.ID = terms.object_id
          WHERE
            a.post_type = 'post'
            AND a.post_status = 'publish'
            AND a.post_parent = 0 
            ${searchCondition || Prisma.empty}
            ${ratingCondition || Prisma.empty}
            ${episodeCondition || Prisma.empty}
            ${viewCondition || Prisma.empty}
        `;
      } else {
        // When no taxonomy filters, skip the filtered_terms join entirely
        totalItemsResult = await this.prisma.$queryRaw<{ count: number }[]>`
          WITH aggregated_meta AS (
            SELECT
              post_id,
              SUM(CAST(CASE WHEN meta_key = 'cap_nhat' THEN meta_value ELSE '0' END AS UNSIGNED)) AS cap_nhat,
              SUM(CAST(CASE WHEN meta_key = '_kksr_casts' THEN meta_value ELSE '0' END AS UNSIGNED)) AS _kksr_casts,
              SUM(CAST(CASE WHEN meta_key = '_kksr_ratings' THEN meta_value ELSE '0' END AS DECIMAL(3,2))) AS _kksr_ratings,
              SUM(CAST(CASE WHEN meta_key = '_kksr_avg' THEN meta_value ELSE '0' END AS DECIMAL(10,2))) AS _kksr_avg,
              SUM(CAST(CASE WHEN meta_key = 'so_tap' THEN meta_value ELSE '0' END AS UNSIGNED)) AS so_tap,
              SUM(CAST(CASE WHEN meta_key = 'views_total' THEN meta_value ELSE '0' END AS UNSIGNED)) AS views_total
            FROM
              wpva_postmeta
            WHERE
              meta_key IN (${Prisma.join(this.arrFieldGetData)})
            GROUP BY
              post_id
          )
          SELECT COUNT(*) AS count
          FROM wpva_posts AS a
          INNER JOIN aggregated_meta AS b ON a.ID = b.post_id
          WHERE
            a.post_type = 'post'
            AND a.post_status = 'publish'
            AND a.post_parent = 0 
            ${searchCondition || Prisma.empty}
            ${ratingCondition || Prisma.empty}
            ${episodeCondition || Prisma.empty}
            ${viewCondition || Prisma.empty}
        `;
      }

      const totalItems = Number(totalItemsResult[0]?.count || 0);
      await this.redisClient.set(cacheKey, totalItems.toString(), {
        EX: cacheTTL,
      });
      this.logger.log(
        `Cached result for key: ${cacheKey} with TTL: ${cacheTTL} seconds`,
      );

      return totalItems;
    } catch (error) {
      this.logger.error(
        `Error in getTotalItems: ${error.message}`,
        error.stack,
      );
      // Trong trường hợp lỗi Redis hoặc truy vấn, trả về 0 hoặc xử lý phù hợp
      throw new Error('Unable to retrieve total items.');
    }
  }

  async getPostIdsFavoritesById(userId: number): Promise<number[]> {
    const favorites = await this.prisma.wpva_usermeta.findFirst({
      where: { user_id: userId, meta_key: 'simplefavorites' },
      select: { meta_value: true },
    });
    console.log(favorites);
    if (!favorites) return [];

    const favValues = HelperUtils.unserializeString(favorites.meta_value);
    return Array.isArray(favValues?.[0]?.posts) ? favValues[0].posts : [];
  }

  async queryAllStory(
    pageSize: number,
    page: number,
    search: string,
    order_by: string,
    sort_by: string,
    req: Request,
    user_id: number | null = null,
    cate_ids: string,
  ): Promise<{ items: StoryEntity[]; paging: Paging }> {
    const offset = (page - 1) * pageSize;
    const useCache = !search;

    this.logger.log(
      `queryAllStory: search="${search}", cate_ids="${cate_ids}", pageSize=${pageSize}, page=${page}`,
    );

    // Normalize search for cache key
    const normalizedSearch = search
      ? encodeURIComponent(search.toLowerCase().trim())
      : 'no_search';
    const cacheKey = `queryAllStory:pageSize=${pageSize}:page=${page}:order_by=${order_by}:sort_by=${sort_by}:user_id=${user_id}:cate_ids=${cate_ids}:search=${normalizedSearch}`;

    // Log cache key for debugging
    this.logCacheKeyInfo('queryAllStory', search, cacheKey);

    let cacheTotalItemKey = '';
    const cacheTTL = 3600;
    if (useCache) {
      cacheTotalItemKey = `queryAllStory:totalItems:user_id=${user_id}:cate_ids=${cate_ids}`;
      const cachedData = await this.redisClient.get(cacheKey);
      if (cachedData) {
        const parsedData = JSON.parse(cachedData);
        const items = await this.convertDetailByPostIds(
          req,
          parsedData.stories,
          user_id,
        );

        return {
          items: items,
          paging: parsedData.paging,
        };
      }
    }

    // Use phrase search instead of individual word search
    const searchCondition = search
      ? Prisma.sql`AND a.post_title REGEXP ${search.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}`
      : Prisma.empty;

    const hasCategoryFilter = cate_ids && cate_ids.trim();
    const cateCondition = hasCategoryFilter
      ? Prisma.sql`
      WHERE tr.term_taxonomy_id IN (${Prisma.join(cate_ids.split(',').map((id) => Number(id)))})
      GROUP BY 
          tr.object_id
      HAVING COUNT(DISTINCT tr.term_taxonomy_id) = ${cate_ids.split(',').length}
      `
      : Prisma.sql`GROUP BY 
                      tr.object_id`;

    this.logger.log(`Has category filter: ${hasCategoryFilter}`);
    this.logger.log(
      `Search condition: ${search ? `LIKE %${search}%` : 'none'}`,
    );

    const totalItems = await this.getTotalItems(
      searchCondition,
      hasCategoryFilter ? cateCondition : undefined,
      undefined,
      undefined,
      undefined,
      cacheTotalItemKey,
      search, // Pass search term for better cache key
    );

    this.logger.log(`Total items found: ${totalItems}`);

    const newArrFieldGetData = [...this.arrFieldGetData];
    const sumCases = this.genQuerySumCasePostMeta(newArrFieldGetData);

    let storiesQuery: Prisma.Sql;

    if (hasCategoryFilter) {
      // When we have category filter, use the terms CTE
      storiesQuery = Prisma.sql`
        WITH terms AS (
          SELECT
                tr.object_id as object_id,
                tt.term_taxonomy_id as term_taxonomy_id
            FROM
                wpva_term_relationships AS tr
            LEFT JOIN wpva_term_taxonomy AS tt ON
                tr.term_taxonomy_id = tt.term_taxonomy_id
            ${cateCondition}
        )
        SELECT
          a.post_date_gmt,
          a.post_date,
          a.ID as post_id,
          a.post_content,
          a.post_title,
          a.post_name,
          a.comment_count,
          a.post_author,
          b.views_daily,
          b.views_monthly,
          b.views_total,
          b.views_weekly,
          b.so_tap,
          b._kksr_avg,
          b._kksr_casts,
          thumb.thumbnail_link as thumbnail_link,
          b._kksr_ratings,
          b.simplefavorites_count
        FROM
          wpva_posts AS a
        INNER JOIN (
          SELECT
            post_id,
            ${sumCases}
          FROM
            wpva_postmeta
          WHERE
            meta_key IN (${Prisma.join(newArrFieldGetData)})
          GROUP BY
            post_id
        ) AS b ON
          a.ID = b.post_id
        LEFT JOIN (
          SELECT
              pm.post_id,
              thumb.guid as thumbnail_link
          FROM
              wpva_postmeta AS pm
          LEFT JOIN wpva_posts AS thumb ON
              pm.meta_value = thumb.ID
          WHERE
              pm.meta_key = '_thumbnail_id'
        ) AS thumb ON
            a.ID = thumb.post_id
        INNER JOIN terms ON
          a.ID = terms.object_id
        WHERE
          a.post_type = 'post' AND a.post_status = 'publish' AND a.post_parent = 0
          ${searchCondition}
          
        ORDER BY
          ${Prisma.raw(order_by)} ${Prisma.raw(sort_by)}
        LIMIT ${pageSize} OFFSET ${offset}
      `;
    } else {
      // When no category filter, skip the terms join entirely
      storiesQuery = Prisma.sql`
        SELECT
          a.post_date_gmt,
          a.post_date,
          a.ID as post_id,
          a.post_content,
          a.post_title,
          a.post_name,
          a.comment_count,
          a.post_author,
          b.views_daily,
          b.views_monthly,
          b.views_total,
          b.views_weekly,
          b.so_tap,
          b._kksr_avg,
          b._kksr_casts,
          thumb.thumbnail_link as thumbnail_link,
          b._kksr_ratings,
          b.simplefavorites_count
        FROM
          wpva_posts AS a
        INNER JOIN (
          SELECT
            post_id,
            ${sumCases}
          FROM
            wpva_postmeta
          WHERE
            meta_key IN (${Prisma.join(newArrFieldGetData)})
          GROUP BY
            post_id
        ) AS b ON
          a.ID = b.post_id
        LEFT JOIN (
          SELECT
              pm.post_id,
              thumb.guid as thumbnail_link
          FROM
              wpva_postmeta AS pm
          LEFT JOIN wpva_posts AS thumb ON
              pm.meta_value = thumb.ID
          WHERE
              pm.meta_key = '_thumbnail_id'
        ) AS thumb ON
            a.ID = thumb.post_id
        WHERE
          a.post_type = 'post' AND a.post_status = 'publish' AND a.post_parent = 0
          ${searchCondition}
          
        ORDER BY
          ${Prisma.raw(order_by)} ${Prisma.raw(sort_by)}
        LIMIT ${pageSize} OFFSET ${offset}
      `;
    }

    this.logger.log(`Executing main query...`);
    const stories = await this.prisma.$queryRaw<StoryEntity[]>(storiesQuery);
    this.logger.log(`Found ${stories.length} stories from query`);

    const items = await this.convertDetailByPostIds(req, stories, user_id);

    const paging: Paging = {
      totalItems,
      totalPages: Math.ceil(totalItems / pageSize),
      currentPage: Number(page),
      pageSize: Number(pageSize),
    };
    if (useCache) {
      await this.redisClient.set(
        cacheKey,
        JSON.stringify({ stories, paging }),
        {
          EX: cacheTTL,
        },
      );
    }

    return {
      items: items,
      paging: paging,
    };
  }

  async findAllStoryNewChapter(
    pageSize: number,
    page: number,
    search: string,
    order_by: string,
    sort_by: string,
    req: Request,
    user_id: number | null = null,
  ): Promise<{ items: StoryEntity[]; paging: Paging }> {
    const useCache = !search;

    const offset = (page - 1) * pageSize;

    this.logger.log(
      `findAllStoryNewChapter: search="${search}", pageSize=${pageSize}, page=${page}`,
    );

    // Normalize search for cache key
    const normalizedSearch = search
      ? encodeURIComponent(search.toLowerCase().trim())
      : 'no_search';
    const cacheKey = `findAllStoryNewChapter:pageSize=${pageSize}:page=${page}:order_by=${order_by}:sort_by=${sort_by}:search=${normalizedSearch}`;
    let cacheTotalItemKey = '';

    const cacheTTL = 3600;
    if (useCache) {
      cacheTotalItemKey = `findAllStoryNewChapter:totalItems`;
      const cachedData = await this.redisClient.get(cacheKey);
      if (cachedData) {
        this.logger.log(`Cache hit for key: ${cacheKey}`);
        const parsedData = JSON.parse(cachedData);
        const items = await this.convertDetailByPostIds(
          req,
          parsedData.stories,
          user_id,
        );

        return {
          items: items,
          paging: parsedData.paging,
        };
      }
    }
    // Use phrase search instead of individual word search
    const searchCondition = search
      ? Prisma.sql`AND a.post_title REGEXP ${search.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}`
      : Prisma.empty;

    const newArrFieldGetData = [...this.arrFieldGetData, 'cap_nhat'];

    const sumCases = this.genQuerySumCasePostMeta(newArrFieldGetData);

    this.logger.log(
      `Search condition: ${search ? `LIKE %${search}%` : 'none'}`,
    );

    const totalItems = await this.getTotalItems(
      searchCondition,
      undefined, // No category filtering in this function
      undefined,
      undefined,
      undefined,
      cacheTotalItemKey,
      search, // Pass search term for better cache key
    );

    this.logger.log(`Total items found: ${totalItems}`);

    // Since this function doesn't have category filtering, skip the terms join entirely
    const stories = await this.prisma.$queryRaw<StoryEntity[]>`
      SELECT
        a.post_date_gmt,
        a.post_date,
        a.ID as post_id,
        a.post_content,
        a.post_title,
        a.post_name,
        a.comment_count,
        a.post_author,
        b.views_daily,
        b.views_monthly,
        b.views_total,
        b.views_weekly,
        b.so_tap,
        b._kksr_avg,
        b._kksr_casts,
        thumb.thumbnail_link as thumbnail_link,
        b._kksr_ratings,
        b.cap_nhat,
        b.simplefavorites_count
      FROM
        wpva_posts AS a
      INNER JOIN (
        SELECT
          post_id,
          ${sumCases}
        FROM
          wpva_postmeta
        WHERE
          meta_key IN (${Prisma.join(newArrFieldGetData)})
        GROUP BY
          post_id
      ) AS b ON
        a.ID = b.post_id
      LEFT JOIN (
        SELECT
          pm.post_id,
              thumb.guid as thumbnail_link
        FROM
          wpva_postmeta AS pm
        LEFT JOIN wpva_posts AS thumb ON
          pm.meta_value = thumb.ID
        WHERE
          pm.meta_key = '_thumbnail_id'
      ) AS thumb ON
        a.ID = thumb.post_id
      WHERE
        a.post_type = 'post' and a.post_status = 'publish' and a.post_parent = 0 ${searchCondition} AND cap_nhat > 0
      ORDER BY
        ${Prisma.raw(order_by)} ${Prisma.raw(sort_by)}
      LIMIT ${pageSize} OFFSET ${offset}
    `;

    this.logger.log(`Found ${stories.length} stories from new chapter query`);

    const items = await this.convertDetailByPostIds(req, stories, user_id);
    const paging: Paging = {
      totalItems,
      totalPages: Math.ceil(totalItems / pageSize),
      currentPage: Number(page),
      pageSize: Number(pageSize),
    };
    if (useCache) {
      await this.redisClient.set(
        cacheKey,
        JSON.stringify({ stories, paging }),
        {
          EX: cacheTTL,
        },
      );
    }

    return {
      items: items,
      paging: paging,
    };
  }

  private async convertDetailByPostIds(
    req: Request,
    stories: StoryEntity[],
    user_id?: number,
  ): Promise<StoryEntity[]> {
    const postIds: number[] = stories.map((story) => story.post_id);
    const items = stories.map((story) => {
      const item = new StoryEntity(story);
      item.userFavorites = false; // Set default value to false
      return item;
    });

    // Nếu user_id tồn tại, lấy danh sách bài viết yêu thích của người dùng
    let postIdsFavorites: number[] = [];
    if (user_id) {
      postIdsFavorites = await this.getPostIdsFavoritesById(user_id);
    }

    // Lấy danh sách bài viết đã được đánh giá bởi fingerprint
    const postIdsRating = await this.getPostRatingByPostIdsFingerprint(
      req,
      postIds,
    );

    // Cập nhật các trường userFavorites và userVoted
    items.forEach((item) => {
      item.userFavorites = postIdsFavorites.includes(item.post_id);
      item.userVoted = postIdsRating.includes(item.post_id);
    });

    return items;
  }

  private parseLinkStoryData(
    data: string,
    so_tap: number,
    storyName: string,
    domain: string,
  ): LinkStoryInterface[] {
    const regex =
      /\[apmap_audio type="audio" path="([^"]+)" title="([^"]+)" download="([^"]+)" encrypt_media_paths="1"\]/g;
    let match;
    let id = 0;
    const result: LinkStoryInterface[] = [];
    console.log(so_tap);
    console.log(data);
    while ((match = regex.exec(data)) !== null) {
      if (id >= so_tap) break;
      id++;
      result.push({
        id: id,
        url: match[1],
        title: `Tap ${`${id}`.padStart(3, '0')}`,
        download: match[3],
        duration: match[4],
      });
    }
    if (data.includes(',')) {
      const urls = data.split(',');
      urls.forEach((url, index) => {
        if (!url.trim()) return;
        result.push({
          id: id++,
          url: url.trim(),
          title: `Tap ${`${index}`.padStart(3, '0')}`,
          download: url.trim(),
          duration: '',
        });
      });
    }
    if (!data) {
      const sanitizedStoryName = storyName
        .normalize('NFD')
        .replace(/[\u0300-\u036f]/g, '')
        .replace(/\s+/g, '')
        .replace(/[\,\!\?\:\-\'\"]/g, '')
        .replace(/[àáảãạăắằẵặẳâầấậẫẩ]/g, 'a')
        .replace(/[ÀÁẢÃẠĂẮẰẴẶẲÂẦẤẬẪẨ]/g, 'A')
        .replace(/[đ]/g, 'd')
        .replace(/[Đ]/g, 'D')
        .replace(/[èéẻẽẹêềếểễệ]/g, 'e')
        .replace(/[ÈÉẺẼẸÊỀẾỂỄỆ]/g, 'E')
        .replace(/[ìíỉĩị]/g, 'i')
        .replace(/[ÌÍỈĨỊ]/g, 'I')
        .replace(/[òóỏõọôồốổỗộơờớởỡợ]/g, 'o')
        .replace(/[ÒÓỎÕỌÔỒỐỔỖỘƠỜỚỞỠỢ]/g, 'O')
        .replace(/[ùúủũụưừứửữự]/g, 'u')
        .replace(/[ÙÚỦŨỤƯỪỨỬỮỰ]/g, 'U')
        .replace(/[ỳýỷỹỵ]/g, 'y')
        .replace(/[ỲÝỶỸỴ]/g, 'Y');

      for (let i = 1; i <= so_tap; i++) {
        const episodeNumber = `${i}`.padStart(3, '0');
        const link = `${domain}${sanitizedStoryName}TH/${episodeNumber}-${sanitizedStoryName}TH.mp3`;
        result.push({
          id: i,
          url: link,
          title: `Tap ${`${i}`.padStart(3, '0')}`,
          download: link,
          duration: '',
        });
      }
    }

    return result;
  }

  private generateLinkStoryServer3(
    storyName: string,
    numberOfEpisodes: number,
    domain: string,
  ): LinkStoryInterface[] {
    const sanitizedStoryName = storyName
      .normalize('NFD')
      .replace(/[\u0300-\u036f]/g, '')
      .replace(/\s+/g, '')
      .replace(/[\,\!\?\:\-\'\"]/g, '')
      .replace(/[àáảãạăắằẵặẳâầấậẫẩ]/g, 'a')
      .replace(/[ÀÁẢÃẠĂẮẰẴẶẲÂẦẤẬẪẨ]/g, 'A')
      .replace(/[đ]/g, 'd')
      .replace(/[Đ]/g, 'D')
      .replace(/[èéẻẽẹêềếểễệ]/g, 'e')
      .replace(/[ÈÉẺẼẸÊỀẾỂỄỆ]/g, 'E')
      .replace(/[ìíỉĩị]/g, 'i')
      .replace(/[ÌÍỈĨỊ]/g, 'I')
      .replace(/[òóỏõọôồốổỗộơờớởỡợ]/g, 'o')
      .replace(/[ÒÓỎÕỌÔỒỐỔỖỘƠỜỚỞỠỢ]/g, 'O')
      .replace(/[ùúủũụưừứửữự]/g, 'u')
      .replace(/[ÙÚỦŨỤƯỪỨỬỮỰ]/g, 'U')
      .replace(/[ỳýỷỹỵ]/g, 'y')
      .replace(/[ỲÝỶỸỴ]/g, 'Y');
    const server3Links: LinkStoryInterface[] = [];

    for (let i = 1; i <= numberOfEpisodes; i++) {
      const episodeNumber = `${i}`.padStart(3, '0');
      const link = `${domain}${sanitizedStoryName}TH/${episodeNumber}-${sanitizedStoryName}TH.mp3`;
      server3Links.push({
        id: i,
        url: link,
        title: `Tap ${`${i}`.padStart(3, '0')}`,
        download: link,
        duration: '',
      });
    }

    return server3Links;
  }

  async getLinksByStoryId(storyId: number) {
    const post = await this.prisma.wpva_posts.findFirst({
      where: {
        ID: storyId,
      },
      select: {
        post_title: true,
      },
    });
    const post_title = post.post_title;

    const postMeta = await this.prisma.wpva_postmeta.findMany({
      where: {
        post_id: storyId,
        meta_key: {
          in: ['audio_playlist_1', 'server_soundcloud', 'so_tap'],
        },
      },
      select: {
        meta_key: true,
        meta_value: true,
      },
    });
    let so_tap = 0;
    const linksStory = postMeta.reduce(
      (acc, link) => {
        if (link.meta_key === 'audio_playlist_1') {
          acc.S1 = link.meta_value;
          acc.S2 = link.meta_value;
        } else if (link.meta_key === 'so_tap') {
          so_tap = Number(link.meta_value);
        }
        return acc;
      },
      { S1: '', S2: '', Svip: '' },
    );
    return new LinksStoryEntity({
      S1: this.parseLinkStoryData(
        linksStory.S1,
        so_tap,
        post_title,
        this.configService.get('DOMAIN_SERVER_1'),
      ),
      S2: this.generateLinkStoryServer3(
        post_title,
        so_tap,
        this.configService.get('DOMAIN_SERVER_2'),
      ),
      Svip: this.generateLinkStoryServer3(
        post_title,
        so_tap,
        this.configService.get('DOMAIN_SERVER_VIP'),
      ),
    });
  }
  async getInformationByStoryID(storyId: number) {
    const informationStories = await this.prisma.$queryRaw<any[]>`
      SELECT 
          wt.name, wt.slug , wtt.taxonomy
      FROM 
          wpva_term_relationships wtr 
      JOIN 
          wpva_term_taxonomy wtt ON wtr.term_taxonomy_id = wtt.term_taxonomy_id 
      JOIN 
          wpva_terms wt ON wtt.term_id = wt.term_id 
      WHERE 
          wtr.object_id = ${storyId}
    `;

    // Convert the result to the desired dictionary format
    const result = informationStories.reduce(
      (acc, item) => {
        let { taxonomy } = item;
        const { name, slug } = item;
        if (taxonomy === 'post_tag') {
          taxonomy = 'author';
        }
        if (!acc[taxonomy]) {
          acc[taxonomy] = [];
        }
        acc[taxonomy].push({ name, slug });
        return acc;
      },
      {} as Record<string, Array<{ name: string; slug: string }>>,
    );

    return result;
  }

  async detailPostByIDS(
    req: Request,
    storyIds: Array<string>,
    userId?: number,
  ): Promise<StoryEntity[]> {
    const newArrFieldGetData = [...this.arrFieldGetData];
    const sumCases = this.genQuerySumCasePostMeta(newArrFieldGetData);
    const stories = await this.prisma.$queryRaw<StoryEntity[]>`
      SELECT
        a.post_date_gmt,
        a.post_date,
        a.ID as post_id,
        a.post_content,
        a.post_title,
        a.post_name,
        a.comment_count,
        a.post_author,
        b.views_daily,
        b.views_monthly,
        b.views_total,
        b.views_weekly,
        b.so_tap,
        b._kksr_avg,
        b._kksr_casts,
        thumb.guid as thumbnail_link,
        b._kksr_ratings
      FROM
        wpva_posts AS a
      INNER JOIN (
        SELECT
          post_id,
          ${sumCases}
        FROM
          wpva_postmeta
        WHERE
          meta_key IN (${Prisma.join(newArrFieldGetData)})
        GROUP BY
          post_id
      ) AS b ON
        a.ID = b.post_id
      LEFT JOIN wpva_postmeta AS pm ON a.ID = pm.post_id AND pm.meta_key = '_thumbnail_id'
      LEFT JOIN wpva_posts AS thumb ON pm.meta_value = thumb.ID
      WHERE
        a.ID IN (${Prisma.join(storyIds)})
      `;
    const items = await this.convertDetailByPostIds(req, stories, userId);
    return items;
  }

  async getBackgroundMusic(): Promise<BackgroundMusicEntity[]> {
    const posts = await this.prisma.wpva_posts.findMany({
      where: {
        post_mime_type: 'audio/mpeg',
      },
      select: {
        post_name: true,
        post_title: true,
        guid: true,
      },
    });
    return posts.map((post) => ({
      name: post.post_name,
      title: post.post_title,
      link: post.guid,
    }));
  }

  getFingerprint(req: Request): string {
    const fingerprint: string | null = null;
    // Check if fingerprint is provided
    if (fingerprint !== null) {
      return fingerprint;
    }

    const ip = req.ip;
    let ipV4 = ip.replace('::ffff:', '');
    this.logger.log(`Fingerprint :: ${ipV4}`);

    const strategies = ['alt_ip_headers']; // Replace with actual configured strategies
    if (strategies.includes('alt_ip_headers')) {
      if (req.headers['cf-connecting-ip']) {
        ipV4 = req.headers['cf-connecting-ip'] as string;
      } else if (req.headers['client-ip']) {
        ipV4 = req.headers['client-ip'] as string;
      } else if (req.headers['x-forwarded-for']) {
        ipV4 = req.headers['x-forwarded-for'] as string;
      }
    }

    return this.generateMd5(ipV4);
  }

  private generateMd5(data: string): string {
    return crypto.createHash('md5').update(data).digest('hex');
  }
  private async getPostRatingByPostIdsFingerprint(
    req: Request,
    postIds: number[],
  ): Promise<number[]> {
    const fingerprint = this.getFingerprint(req);

    const ratings = await this.prisma.wpva_postmeta.findMany({
      where: {
        post_id: {
          in: postIds,
        },
        meta_key: '_kksr_fingerprint_default',
        meta_value: fingerprint,
      },
      select: {
        post_id: true,
      },
    });
    return ratings.map((rating) => Number(rating.post_id));
  }

  async votePostId(
    req: Request,
    postId: number,
    type_vote: string,
    rating: number,
  ) {
    this.logger.log(
      `StoryService :: votePostId ${postId}, type ${type_vote}, rating ${rating}`,
    );
    const fpId = this.getFingerprint(req);
    this.logger.log(`StoryService :: fpId ${fpId}`);
    const checkExistVote = await this.prisma.wpva_postmeta.findMany({
      where: {
        post_id: postId,
        meta_key: {
          in: [
            '_kksr_fingerprint_default',
            '_kksr_avg',
            '_kksr_avg_default',
            '_kksr_casts',
            '_kksr_count_default',
            '_kksr_ratings',
            '_kksr_ratings_default',
            '_kksr_ref',
          ],
        },
      },
    });
    const resultDict = checkExistVote.reduce(
      (acc, item) => {
        acc[item.meta_key] = item.meta_value;
        return acc;
      },
      {} as { [key: string]: any },
    );
    console.log(resultDict);

    // Kiểm tra sự tồn tại của _kksr_ratings và _kksr_casts
    const hasRatings = resultDict.hasOwnProperty('_kksr_ratings');
    const hasCasts = resultDict.hasOwnProperty('_kksr_casts');

    if (
      !hasRatings ||
      !hasCasts ||
      resultDict['_kksr_fingerprint_default'] != fpId
    ) {
      const ratings = hasRatings ? Number(resultDict['_kksr_ratings']) : 0;
      const casts = hasCasts ? Number(resultDict['_kksr_casts']) : 0;

      if (type_vote === 'up') {
        const nRating = rating + ratings;
        const nCasts = casts + 1;
        const nAvg = nRating / nCasts;

        const updatePostMeta = async (
          data: {
            post_id: number;
            meta_key: string;
            meta_value: number | string;
          }[],
        ) => {
          const promises = data.map((item) =>
            this.prisma.wpva_postmeta.updateMany({
              where: {
                post_id: item.post_id,
                meta_key: item.meta_key,
              },
              data: {
                meta_value: String(item.meta_value),
              },
            }),
          );

          const result = await Promise.all(promises);
          return result;
        };

        // Dữ liệu của bạn
        const postMetaData = [
          { post_id: postId, meta_key: '_kksr_avg', meta_value: String(nAvg) },
          {
            post_id: postId,
            meta_key: '_kksr_avg_default',
            meta_value: String(nAvg),
          },
          {
            post_id: postId,
            meta_key: '_kksr_casts',
            meta_value: String(nCasts),
          },
          {
            post_id: postId,
            meta_key: '_kksr_count_default',
            meta_value: String(nCasts),
          },
          {
            post_id: postId,
            meta_key: '_kksr_ratings',
            meta_value: String(nRating),
          },
          {
            post_id: postId,
            meta_key: '_kksr_ratings_default',
            meta_value: String(nRating),
          },
        ];

        if (!hasRatings || !hasCasts) {
          await this.prisma.wpva_postmeta.createMany({
            data: postMetaData,
          });
        } else {
          await updatePostMeta(postMetaData);
        }

        await this.prisma.wpva_postmeta.createMany({
          data: [
            {
              post_id: postId,
              meta_key: '_kksr_fingerprint_default',
              meta_value: fpId,
            },
            {
              post_id: postId,
              meta_key: '_kksr_ref',
              meta_value: fpId,
            },
          ],
        });
      }
    }
    return {};
  }

  private serializeBody(obj: any): string {
    if (!obj || typeof obj !== 'object') return '';

    // More efficient serialization using a stable hash approach
    const normalizedObj = this.normalizeFilterObject(obj);
    return JSON.stringify(normalizedObj);
  }

  private normalizeFilterObject(obj: FindStoryConditionQueryDto): any {
    const normalized: any = {};

    // Only include non-empty arrays and valid filter objects
    if (obj.genres?.length)
      normalized.genres = obj.genres.sort((a, b) => a - b);
    if (obj.storyType?.length)
      normalized.storyType = obj.storyType.sort((a, b) => a - b);
    if (obj.status?.length)
      normalized.status = obj.status.sort((a, b) => a - b);
    if (obj.server?.length)
      normalized.server = obj.server.sort((a, b) => a - b);

    if (
      obj.rating?.ratingFrom !== undefined &&
      obj.rating?.ratingTo !== undefined
    ) {
      normalized.rating = {
        from: obj.rating.ratingFrom,
        to: obj.rating.ratingTo,
      };
    }
    if (
      obj.episodes?.episodeFrom !== undefined &&
      obj.episodes?.episodeTo !== undefined
    ) {
      normalized.episodes = {
        from: obj.episodes.episodeFrom,
        to: obj.episodes.episodeTo,
      };
    }
    if (obj.views?.viewForm !== undefined && obj.views?.viewTo !== undefined) {
      normalized.views = {
        from: obj.views.viewForm,
        to: obj.views.viewTo,
      };
    }

    return normalized;
  }

  private buildFilterConditions(
    body: FindStoryConditionQueryDto,
    search: string,
  ) {
    // Use phrase search instead of individual word search
    const searchCondition = search
      ? Prisma.sql`AND a.post_title REGEXP ${search.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}`
      : Prisma.empty;

    // Optimize taxonomy filtering - combine all taxonomy IDs efficiently
    const combinedTaxonomyIds = [
      ...(body.genres?.filter(Boolean) || []),
      ...(body.storyType?.filter(Boolean) || []),
      ...(body.status?.filter(Boolean) || []),
      ...(body.server?.filter(Boolean) || []),
    ];

    // Only create combinedCondition if we have taxonomy filters
    const combinedCondition = combinedTaxonomyIds.length
      ? Prisma.sql`
        WHERE tr.term_taxonomy_id IN (${Prisma.join(combinedTaxonomyIds)})
        GROUP BY tr.object_id
        HAVING COUNT(DISTINCT tr.term_taxonomy_id) = ${combinedTaxonomyIds.length}
        `
      : undefined; // Return undefined instead of GROUP BY when no filters

    const ratingCondition =
      body.rating?.ratingFrom !== undefined &&
      body.rating?.ratingTo !== undefined
        ? Prisma.sql`AND _kksr_avg BETWEEN ${body.rating.ratingFrom} AND ${body.rating.ratingTo}`
        : Prisma.empty;

    const episodeCondition =
      body.episodes?.episodeFrom !== undefined &&
      body.episodes?.episodeTo !== undefined
        ? Prisma.sql`AND so_tap BETWEEN ${body.episodes.episodeFrom} AND ${body.episodes.episodeTo}`
        : Prisma.empty;

    const viewCondition =
      body.views?.viewForm !== undefined && body.views?.viewTo !== undefined
        ? Prisma.sql`AND views_total BETWEEN ${body.views.viewForm} AND ${body.views.viewTo}`
        : Prisma.empty;

    return {
      searchCondition,
      combinedCondition,
      ratingCondition,
      episodeCondition,
      viewCondition,
      hasTaxonomyFilter: combinedTaxonomyIds.length > 0,
    };
  }

  async storiesActionsFilter(
    req: Request,
    body: FindStoryConditionQueryDto,
    pageSize: number,
    page: number,
    search: string,
    order_by: string,
    sort_by: string,
    userId?: number,
  ): Promise<{ items: StoryEntity[]; paging: Paging }> {
    const offset = (page - 1) * pageSize;
    const useCache = !search && pageSize <= 50; // Only cache reasonable page sizes

    this.logger.log(
      `storiesActionsFilter: search="${search}", pageSize=${pageSize}, page=${page}`,
    );
    this.logger.log(`Filter body:`, JSON.stringify(body));

    // More efficient cache key generation
    const bodyHash = this.serializeBody(body);
    // Normalize search for cache key
    const normalizedSearch = search
      ? encodeURIComponent(search.toLowerCase().trim())
      : 'no_search';
    const cacheKey = `storiesFilter:${pageSize}:${page}:${order_by}:${sort_by}:${bodyHash}:search=${normalizedSearch}`;
    const cacheTotalItemKey = useCache
      ? `storiesFilter:total:${bodyHash}:search=${normalizedSearch}`
      : '';

    const cacheTTL = 600;

    // Try cache first if applicable
    if (useCache) {
      try {
        const cachedData = await this.redisClient.get(cacheKey);
        if (cachedData) {
          this.logger.log(`Cache hit for storiesActionsFilter: ${cacheKey}`);
          const parsedData = JSON.parse(cachedData);
          const items = await this.convertDetailByPostIds(
            req,
            parsedData.stories,
            userId,
          );
          return { items, paging: parsedData.paging };
        }
      } catch (error) {
        this.logger.warn(`Cache read error: ${error.message}`);
      }
    }

    // Build filter conditions
    const conditions = this.buildFilterConditions(body, search);

    // Check if we have any taxonomy filters
    const combinedTaxonomyIds = [
      ...(body.genres?.filter(Boolean) || []),
      ...(body.storyType?.filter(Boolean) || []),
      ...(body.status?.filter(Boolean) || []),
      ...(body.server?.filter(Boolean) || []),
    ];
    const hasTaxonomyFilter = combinedTaxonomyIds.length > 0;

    this.logger.log(
      `Has taxonomy filter: ${hasTaxonomyFilter}, taxonomy IDs: [${combinedTaxonomyIds.join(', ')}]`,
    );
    this.logger.log(
      `Search condition: ${search ? `LIKE %${search}%` : 'none'}`,
    );

    // Get total items count
    const totalItems = await this.getTotalItems(
      conditions.searchCondition,
      conditions.combinedCondition,
      conditions.ratingCondition,
      conditions.episodeCondition,
      conditions.viewCondition,
      cacheTotalItemKey,
      search, // Pass search term for better cache key
    );

    this.logger.log(`Total items found: ${totalItems}`);

    // Early return if no items found
    if (totalItems === 0) {
      const emptyResult = {
        items: [],
        paging: {
          totalItems: 0,
          totalPages: 0,
          currentPage: Number(page),
          pageSize: Number(pageSize),
        },
      };

      if (useCache) {
        try {
          await this.redisClient.set(
            cacheKey,
            JSON.stringify({ stories: [], paging: emptyResult.paging }),
            { EX: cacheTTL },
          );
        } catch (error) {
          this.logger.warn(`Cache write error: ${error.message}`);
        }
      }

      return emptyResult;
    }

    // Build and execute main query
    const newArrFieldGetData = [...this.arrFieldGetData];
    const sumCases = this.genQuerySumCasePostMeta(newArrFieldGetData);

    let stories: StoryEntity[];

    if (hasTaxonomyFilter) {
      // When we have taxonomy filter, use the terms CTE
      stories = await this.prisma.$queryRaw<StoryEntity[]>`
        WITH terms AS (
          SELECT
            tr.object_id as object_id,
            tt.term_taxonomy_id as term_taxonomy_id
          FROM
            wpva_term_relationships AS tr
          LEFT JOIN wpva_term_taxonomy AS tt ON
            tr.term_taxonomy_id = tt.term_taxonomy_id
          ${conditions.combinedCondition}
        )
        SELECT
          a.post_date_gmt,
          a.post_date,
          a.ID as post_id,
          a.post_content,
          a.post_title,
          a.post_name,
          a.comment_count,
          a.post_author,
          b.views_daily,
          b.views_monthly,
          b.views_total,
          b.views_weekly,
          b.so_tap,
          b._kksr_avg,
          b._kksr_casts,
          thumb.thumbnail_link as thumbnail_link,
          b._kksr_ratings,
          b.simplefavorites_count
        FROM
          wpva_posts AS a
        INNER JOIN (
          SELECT
            post_id,
            ${sumCases}
          FROM
            wpva_postmeta
          WHERE
            meta_key IN (${Prisma.join(newArrFieldGetData)})
          GROUP BY
            post_id
        ) AS b ON
          a.ID = b.post_id
        LEFT JOIN (
          SELECT
            pm.post_id,
            thumb.guid as thumbnail_link
          FROM
            wpva_postmeta AS pm
          LEFT JOIN wpva_posts AS thumb ON
            pm.meta_value = thumb.ID
          WHERE
            pm.meta_key = '_thumbnail_id'
        ) AS thumb ON
          a.ID = thumb.post_id
        INNER JOIN terms ON
          a.ID = terms.object_id
        WHERE
          a.post_type = 'post' AND a.post_status = 'publish' AND a.post_parent = 0
          ${conditions.searchCondition}
          ${conditions.ratingCondition}
          ${conditions.episodeCondition}
          ${conditions.viewCondition}
        ORDER BY
          ${Prisma.raw(order_by)} ${Prisma.raw(sort_by)}
        LIMIT ${pageSize} OFFSET ${offset}
      `;
    } else {
      // When no taxonomy filter, skip the terms join entirely
      stories = await this.prisma.$queryRaw<StoryEntity[]>`
        SELECT
          a.post_date_gmt,
          a.post_date,
          a.ID as post_id,
          a.post_content,
          a.post_title,
          a.post_name,
          a.comment_count,
          a.post_author,
          b.views_daily,
          b.views_monthly,
          b.views_total,
          b.views_weekly,
          b.so_tap,
          b._kksr_avg,
          b._kksr_casts,
          thumb.thumbnail_link as thumbnail_link,
          b._kksr_ratings,
          b.simplefavorites_count
        FROM
          wpva_posts AS a
        INNER JOIN (
          SELECT
            post_id,
            ${sumCases}
          FROM
            wpva_postmeta
          WHERE
            meta_key IN (${Prisma.join(newArrFieldGetData)})
          GROUP BY
            post_id
        ) AS b ON
          a.ID = b.post_id
        LEFT JOIN (
          SELECT
            pm.post_id,
            thumb.guid as thumbnail_link
          FROM
            wpva_postmeta AS pm
          LEFT JOIN wpva_posts AS thumb ON
            pm.meta_value = thumb.ID
          WHERE
            pm.meta_key = '_thumbnail_id'
        ) AS thumb ON
          a.ID = thumb.post_id
        WHERE
          a.post_type = 'post' AND a.post_status = 'publish' AND a.post_parent = 0
          ${conditions.searchCondition}
          ${conditions.ratingCondition}
          ${conditions.episodeCondition}
          ${conditions.viewCondition}
        ORDER BY
          ${Prisma.raw(order_by)} ${Prisma.raw(sort_by)}
        LIMIT ${pageSize} OFFSET ${offset}
      `;
    }

    this.logger.log(`Found ${stories.length} stories from filter query`);

    const items = await this.convertDetailByPostIds(req, stories, userId);
    const paging: Paging = {
      totalItems,
      totalPages: Math.ceil(totalItems / pageSize),
      currentPage: Number(page),
      pageSize: Number(pageSize),
    };

    // Cache the result if applicable
    if (useCache) {
      try {
        await this.redisClient.set(
          cacheKey,
          JSON.stringify({ stories, paging }),
          { EX: cacheTTL },
        );
        this.logger.log(`Cached storiesActionsFilter result: ${cacheKey}`);
      } catch (error) {
        this.logger.warn(`Cache write error: ${error.message}`);
      }
    }

    return { items, paging };
  }
}
