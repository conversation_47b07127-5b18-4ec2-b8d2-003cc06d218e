// src/auth/hashing/hashing.service.ts

import { Injectable } from '@nestjs/common';
import { PasswordHash } from 'phpass';

@Injectable()
export class HashingService {
  private passwordHasher: PasswordHash;

  constructor() {
    // Khởi tạo phpass với các tham số mặc định của WordPress
    // 8 là số vòng lặp, true là để tương thích với các hệ thống cũ hơn
    this.passwordHasher = new PasswordHash(8, true);
  }

  /**
   * Hash một mật khẩu theo chuẩn WordPress.
   * @param password Mật khẩu dạng chuỗi thuần.
   * @returns Mật khẩu đã được hash.
   */
  hashPassword(password: string): string {
    return this.passwordHasher.HashPassword(password);
  }

  /**
   * Kiểm tra một mật khẩu thuần có khớp với mật khẩu đã hash của WordPress không.
   * @param password Mật khẩu dạng chuỗi thuần.
   * @param storedHash Mật khẩu đã được hash lấy từ CSDL WordPress.
   * @returns `true` nếu khớp, ngược lại là `false`.
   */
  checkPassword(password: string, storedHash: string): boolean {
    return this.passwordHasher.CheckPassword(password, storedHash);
  }
}
